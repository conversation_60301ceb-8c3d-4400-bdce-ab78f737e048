[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\HarryPotter\\HarryPotter\\build\\Desktop_Qt_6_5_3_MinGW_64_bit-Debug\\HarryPotter_autogen\\include", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtCore", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtWidgets", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtGui", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "H:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "G:\\HarryPotter\\HarryPotter\\main.cpp"], "directory": "G:/HarryPotter/HarryPotter/build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "G:/<PERSON><PERSON><PERSON><PERSON>/Harry<PERSON>otter/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\HarryPotter\\HarryPotter\\build\\Desktop_Qt_6_5_3_MinGW_64_bit-Debug\\HarryPotter_autogen\\include", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtCore", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtWidgets", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtGui", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "H:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "G:\\HarryPotter\\HarryPotter\\mainwindow.cpp"], "directory": "G:/HarryPotter/HarryPotter/build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "G:/Harry<PERSON><PERSON>r/Harry<PERSON>otter/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IH:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\HarryPotter\\HarryPotter\\build\\Desktop_Qt_6_5_3_MinGW_64_bit-Debug\\HarryPotter_autogen\\include", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtCore", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtWidgets", "-isystem", "H:\\Qt\\6.5.3\\mingw_64\\include\\QtGui", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "H:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "H:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "G:\\HarryPotter\\HarryPotter\\mainwindow.h"], "directory": "G:/HarryPotter/HarryPotter/build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "G:/<PERSON><PERSON><PERSON><PERSON>/Harry<PERSON>otter/mainwindow.h"}]