{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-3b7dec74e2df5c307404.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "<PERSON><PERSON><PERSON><PERSON>", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "HarryPotter::@6890427a1f51a3e7e1df", "jsonFile": "target-HarryPotter-Debug-5170f32d61057db38a7f.json", "name": "<PERSON><PERSON><PERSON><PERSON>", "projectIndex": 0}, {"directoryIndex": 0, "id": "HarryPotter_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-Harry<PERSON><PERSON>r_autogen-Debug-51cf54540028b9d8c35d.json", "name": "Harry<PERSON>otter_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "<PERSON><PERSON><PERSON><PERSON>_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-<PERSON><PERSON><PERSON><PERSON>_autogen_timestamp_deps-Debug-32ed1204bcf87701d4d7.json", "name": "<PERSON><PERSON><PERSON><PERSON>_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "G:/Harry<PERSON>otter/HarryPotter/build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug", "source": "G:/<PERSON><PERSON><PERSON><PERSON>/<PERSON>r"}, "version": {"major": 2, "minor": 7}}