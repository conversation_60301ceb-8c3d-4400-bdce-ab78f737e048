{"artifacts": [{"path": "HarryPotter.exe"}, {"path": "HarryPotter.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies"], "files": ["H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "H:/Qt/6.5.3/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 23, "parent": 0}, {"command": 2, "file": 0, "line": 741, "parent": 1}, {"command": 1, "file": 0, "line": 549, "parent": 2}, {"command": 0, "file": 0, "line": 588, "parent": 3}, {"command": 4, "file": 1, "line": 64, "parent": 0}, {"command": 5, "file": 1, "line": 47, "parent": 0}, {"command": 8, "file": 1, "line": 13, "parent": 0}, {"file": 4, "parent": 7}, {"command": 8, "file": 4, "line": 157, "parent": 8}, {"file": 3, "parent": 9}, {"command": 7, "file": 3, "line": 52, "parent": 10}, {"file": 2, "parent": 11}, {"command": 6, "file": 2, "line": 61, "parent": 12}, {"command": 5, "file": 0, "line": 550, "parent": 2}, {"command": 7, "file": 3, "line": 40, "parent": 10}, {"file": 9, "parent": 15}, {"command": 10, "file": 9, "line": 39, "parent": 16}, {"command": 9, "file": 8, "line": 111, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 18}, {"file": 6, "parent": 19}, {"command": 7, "file": 6, "line": 52, "parent": 20}, {"file": 5, "parent": 21}, {"command": 6, "file": 5, "line": 61, "parent": 22}, {"command": 7, "file": 6, "line": 40, "parent": 20}, {"file": 12, "parent": 24}, {"command": 10, "file": 12, "line": 39, "parent": 25}, {"command": 9, "file": 8, "line": 111, "parent": 26}, {"command": 8, "file": 7, "line": 76, "parent": 27}, {"file": 11, "parent": 28}, {"command": 7, "file": 11, "line": 52, "parent": 29}, {"file": 10, "parent": 30}, {"command": 6, "file": 10, "line": 61, "parent": 31}, {"command": 6, "file": 10, "line": 76, "parent": 31}, {"command": 9, "file": 8, "line": 111, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 34}, {"file": 14, "parent": 35}, {"command": 7, "file": 14, "line": 52, "parent": 36}, {"file": 13, "parent": 37}, {"command": 6, "file": 13, "line": 61, "parent": 38}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 14, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 14, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 14, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 14, "define": "UNICODE"}, {"backtrace": 14, "define": "WIN32"}, {"backtrace": 14, "define": "WIN64"}, {"backtrace": 14, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 14, "define": "_UNICODE"}, {"backtrace": 14, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "G:/HarryPotter/HarryPotter/build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/HarryPotter_autogen/include"}, {"backtrace": 14, "isSystem": true, "path": "H:/Qt/6.5.3/mingw_64/include/QtCore"}, {"backtrace": 14, "isSystem": true, "path": "H:/Qt/6.5.3/mingw_64/include"}, {"backtrace": 14, "isSystem": true, "path": "H:/Qt/6.5.3/mingw_64/mkspecs/win32-g++"}, {"backtrace": 6, "isSystem": true, "path": "H:/Qt/6.5.3/mingw_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "H:/Qt/6.5.3/mingw_64/include/QtGui"}], "language": "CXX", "languageStandard": {"backtraces": [14, 14], "standard": "17"}, "sourceIndexes": [0, 1, 2, 5]}], "dependencies": [{"id": "<PERSON><PERSON><PERSON><PERSON>_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "HarryPotter_autogen::@6890427a1f51a3e7e1df"}], "id": "HarryPotter::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/Harry<PERSON>otter"}}, "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "-mwindows", "role": "flags"}, {"backtrace": 6, "fragment": "H:\\Qt\\6.5.3\\mingw_64\\lib\\libQt6Widgets.a", "role": "libraries"}, {"backtrace": 13, "fragment": "H:\\Qt\\6.5.3\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 14, "fragment": "H:\\Qt\\6.5.3\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 23, "fragment": "-lmpr", "role": "libraries"}, {"backtrace": 23, "fragment": "-l<PERSON><PERSON>v", "role": "libraries"}, {"backtrace": 32, "fragment": "-lmingw32", "role": "libraries"}, {"backtrace": 32, "fragment": "H:\\Qt\\6.5.3\\mingw_64\\lib\\libQt6EntryPoint.a", "role": "libraries"}, {"backtrace": 33, "fragment": "-lshell32", "role": "libraries"}, {"backtrace": 39, "fragment": "-ld3d11", "role": "libraries"}, {"backtrace": 39, "fragment": "-ldxgi", "role": "libraries"}, {"backtrace": 39, "fragment": "-ldxguid", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "nameOnDisk": "HarryPotter.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 5]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 6]}, {"name": "Forms", "sourceIndexes": [4]}, {"name": "", "sourceIndexes": [7]}, {"name": "Resources", "sourceIndexes": [8]}, {"name": "CMake Rules", "sourceIndexes": [9]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/HarryPotter_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "mainwindow.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/qrc_res.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/HarryPotter_autogen/include/ui_mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/HarryPotter_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "path": "res.qrc", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_MinGW_64_bit-Debug/HarryPotter_autogen/timestamp.rule", "sourceGroupIndex": 5}], "type": "EXECUTABLE"}